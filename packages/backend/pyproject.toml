[project]
name = "backend"
version = "0.1.0"
description = "Backend package for the project"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "fastapi[standard]>=0.115.11",
    "httpx>=0.27.0",
    "faststream[redis]>=0.5.42",
    "demoparser2>=0.38.0",
    "awpy>=2.0.2",
    "structlog>=24.5.0",
    "aiofiles>=24.1.0",
    "anyio>=4.8.0",
    "typer>=0.15.1",
    "rich>=13.9.4",
    "result>=0.17.0",
    "pandas-stubs>=2.2.3.250527",
    "sqlalchemy>=2.0.0",
    "sqlmodel>=0.0.24",
    "pydantic-settings>=2.8.1",
]

[dependency-groups]
dev = [
    "basedpyright>=1.29.1",
    "pytest>=8.3.5",
    "ruff>=0.9.9",
]
testing = [
    "pytest>=8.3.5",
    "pytest-asyncio>=0.25.3",
    "testcontainers>=4.8.3",
    "psycopg2-binary>=2.9.10",
    "pytest-cov>=6.1.1",
]

[project.scripts]
brainless-cli = "backend.cli:app"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
