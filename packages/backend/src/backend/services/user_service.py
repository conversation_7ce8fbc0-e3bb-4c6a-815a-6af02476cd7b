"""User management service."""

from fastapi import Response, status

from backend import crud, schemas
from backend.clients.steam_api import SteamAPI
from backend.database import get_connection
from backend.logging_config import get_logger

logger = get_logger(__name__)


class UserService:
    """Service for managing users and tracking details."""

    def __init__(self, steam_api: SteamAPI):
        """Initialize the user service.

        Args:
            steam_api: Steam API client
        """
        self.steam_api = steam_api

    async def register_user(
        self, user: schemas.UserCreate, response: Response
    ) -> dict | None:
        """Register a new user.

        Args:
            user: User data to create
            response: FastAPI response object

        Returns:
            The created user or None if the user already exists
        """
        with get_connection() as conn:
            created_user = await crud.create_user(user, conn, self.steam_api)
            if created_user is None:
                response.status_code = status.HTTP_409_CONFLICT
                return None
            return created_user.model_dump()

    def get_user_by_steam_id(self, steam_id: str) -> schemas.UserRead | None:
        """Get a user by their Steam ID.

        Args:
            steam_id: The Steam ID of the user to retrieve

        Returns:
            The user with the specified Steam ID
        """
        with get_connection() as conn:
            return crud.read_user_by_steam_id(steam_id, conn)

    def check_user_registered(self, steam_id: str) -> bool:
        """Check if a user is registered.

        Args:
            steam_id: The Steam ID to check

        Returns:
            True if the user is registered, False otherwise
        """
        with get_connection() as conn:
            return crud.is_user_registered(steam_id, conn)

    def get_user(self, user_id: int) -> schemas.UserReadWithTracking:
        """Get a user by their ID.

        Args:
            user_id: The ID of the user to retrieve

        Returns:
            The user with the specified ID

        Raises:
            HTTPException: If user is not found
        """
        with get_connection() as conn:
            return crud.read_user(user_id, conn)

    def get_user_tracking(self, user_id: int) -> schemas.TrackingDetailsRead:
        """Get tracking details for a user.

        Args:
            user_id: The ID of the user to get tracking details for

        Returns:
            The tracking details for the user

        Raises:
            HTTPException: If user is not found or has no tracking
        """
        with get_connection() as conn:
            return crud.read_tracking_details(user_id, conn)

    def create_user_tracking(
        self, user_id: int, tracking: schemas.TrackingDetailsCreate
    ) -> schemas.TrackingDetailsRead:
        """Create tracking details for a user.

        Args:
            user_id: The ID of the user to create tracking details for
            tracking: The tracking details to create

        Returns:
            The created tracking details

        Raises:
            HTTPException: If user is not found or already has tracking
        """
        with get_connection() as conn:
            return crud.create_tracking_details(user_id, tracking, conn)
